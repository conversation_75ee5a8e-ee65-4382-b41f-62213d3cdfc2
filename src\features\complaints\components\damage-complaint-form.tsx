'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { useAgencyTranslations } from '@/hooks/use-translations';
import { AGENCY_CODES } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import {
    useCreateComplaint,
    useUpdateComplaint,
} from '../hooks/use-complaints-simple';
import { CreateComplaintInput, createComplaintInputSchema } from '../schemas';

interface DamageComplaintFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  editMode?: boolean;
  initialData?: Partial<CreateComplaintInput & { repairCostRm?: string }>;
  complaintId?: string;
}

export function DamageComplaintForm({
  onSuccess,
  onCancel,
  editMode = false,
  initialData,
  complaintId,
}: DamageComplaintFormProps) {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [beforePhotos, setBeforePhotos] = useState<File[]>([]);
  const [afterPhotos, setAfterPhotos] = useState<File[]>([]);
  const createComplaintMutation = useCreateComplaint();
  const updateComplaintMutation = useUpdateComplaint();
  const tAgency = useAgencyTranslations();
  const t = useTranslations('complaints.form');

  const form = useForm<CreateComplaintInput>({
    resolver: zodResolver(createComplaintInputSchema),
    defaultValues: {
      email: initialData?.email || '',
      damageComplaintDate: initialData?.damageComplaintDate || new Date(),
      expectedCompletionDate: initialData?.expectedCompletionDate || new Date(),
      agency: initialData?.agency || '',
      contractorCompanyName: initialData?.contractorCompanyName || '',
      location: initialData?.location || '',
      description: initialData?.description || '',
      mantrapLocation: initialData?.mantrapLocation || '',
      contactNumber: initialData?.contactNumber || '',
      contractorEmail: initialData?.contractorEmail || '',
      contractorContactNumber: initialData?.contractorContactNumber || '',
      noPmaLif: initialData?.noPmaLif || '',
      actualCompletionDate: initialData?.actualCompletionDate || undefined,
      completionNotes: initialData?.completionNotes || '',      status: (initialData?.status || 'open') as
        | 'open'
        | 'closed',
      statusNotes: initialData?.statusNotes || '',
      contractorNotes: initialData?.contractorNotes || '',
      repairCostRm: initialData?.repairCostRm || '',
    },
  });

  const _handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter((file) => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      const isValidType = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/pdf',
      ].includes(file.type);
      return isValidSize && isValidType;
    });

    if (uploadedFiles.length + validFiles.length > 5) {
      alert('Maximum 5 files allowed');
      return;
    }

    setUploadedFiles((prev) => [...prev, ...validFiles]);
  };

  const _removeFile = (index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleBeforePhotoUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter((file) => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      const isValidType = ['image/jpeg', 'image/png', 'image/jpg'].includes(
        file.type,
      );
      return isValidSize && isValidType;
    });
    setBeforePhotos((prev) => [...prev, ...validFiles]);
  };

  const handleAfterPhotoUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter((file) => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      const isValidType = ['image/jpeg', 'image/png', 'image/jpg'].includes(
        file.type,
      );
      return isValidSize && isValidType;
    });
    setAfterPhotos((prev) => [...prev, ...validFiles]);
  };
  const onSubmit = async (data: CreateComplaintInput) => {
    try {
      if (editMode && complaintId) {
        await updateComplaintMutation.mutateAsync({
          id: complaintId,
          data: {
            ...data,
            proofOfRepairFiles: [
              ...uploadedFiles,
              ...beforePhotos,
              ...afterPhotos,
            ],
            repairCostRm: data.repairCostRm,
          },
        });
      } else {
        await createComplaintMutation.mutateAsync({
          ...data,
          proofOfRepairFiles: [
            ...uploadedFiles,
            ...beforePhotos,
            ...afterPhotos,
          ],
          repairCostRm: data.repairCostRm,
        });
      }
      onSuccess();
    } catch (error) {
      console.error(
        editMode
          ? 'Failed to update complaint:'
          : 'Failed to create complaint:',
        error,
      );
    }
  };
  return (
    <div className="container mx-auto p-6 space-y-6 max-w-6xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('title')}</h1>
        <p className="text-gray-600">{t('subtitle')}</p>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* A. Maklumat Aduan */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4 bg-blue-50">
              <CardTitle className="text-lg font-semibold text-blue-800 flex items-center gap-2">
                {t('sectionA.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.email')}
                </Label>
                <Input
                  id="email"
                  type="email"
                  {...form.register('email')}
                  className={cn(
                    'h-10',
                    form.formState.errors.email &&
                      'border-red-500 focus-visible:ring-red-500',
                  )}
                  placeholder={t('placeholders.email')}
                />
                {form.formState.errors.email && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.email.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="contactNumber"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.name')}
                </Label>
                <Input
                  id="contactNumber"
                  {...form.register('contactNumber')}
                  className="h-10"
                  placeholder="Ahmad Zainuddin"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.complaintDate')}
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full h-10 justify-start text-left font-normal',
                        !form.watch('damageComplaintDate') &&
                          'text-muted-foreground',
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {form.watch('damageComplaintDate') ? (
                        format(form.watch('damageComplaintDate'), 'yyyy-MM-dd')
                      ) : (
                        <span>{t('placeholders.selectDate')}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={form.watch('damageComplaintDate')}
                      onSelect={(date) =>
                        form.setValue('damageComplaintDate', date || new Date())
                      }
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.agency')}
                </Label>
                <Select
                  onValueChange={(value) => form.setValue('agency', value)}
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder={t('placeholders.selectAgency')} />
                  </SelectTrigger>
                  <SelectContent>
                    {AGENCY_CODES.map((agencyCode) => (
                      <SelectItem key={agencyCode} value={agencyCode}>
                        {tAgency(agencyCode)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="contractorCompanyName"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.contractorCompanyName')}
                </Label>
                <Input
                  id="contractorCompanyName"
                  {...form.register('contractorCompanyName')}
                  className="h-10"
                  placeholder="Syarikat Lif Teknologi Sdn Bhd"
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="location"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.location')}
                </Label>
                <Input
                  id="location"
                  {...form.register('location')}
                  className="h-10"
                  placeholder=""
                />
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="noPmaLif"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionA.pmaNumber')}
                </Label>
                <Input
                  id="noPmaLif"
                  {...form.register('noPmaLif')}
                  className="h-10"
                  placeholder=""
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.damageDescription')}
                </Label>
                <Input
                  {...form.register('description')}
                  className="h-10"
                  placeholder=""
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.expectedCompletionDate')}
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full h-10 justify-start text-left font-normal',
                        !form.watch('expectedCompletionDate') &&
                          'text-muted-foreground',
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {form.watch('expectedCompletionDate') ? (
                        format(
                          form.watch('expectedCompletionDate'),
                          'yyyy-MM-dd',
                        )
                      ) : (
                        <span>{t('placeholders.selectDate')}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={form.watch('expectedCompletionDate')}
                      onSelect={(date) =>
                        form.setValue(
                          'expectedCompletionDate',
                          date || new Date(),
                        )
                      }
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionA.involvesManTrap')}
                </Label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="mantrap"
                      value="Ya"
                      className="w-4 h-4"
                    />
                    <span className="text-sm">{t('sectionA.yes')}</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="mantrap"
                      value="Tidak"
                      className="w-4 h-4"
                      defaultChecked
                    />
                    <span className="text-sm">{t('sectionA.no')}</span>
                  </label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* B. Maklumat Pembetulan */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4 bg-green-50">
              <CardTitle className="text-lg font-semibold text-green-800 flex items-center gap-2">
                {t('sectionB.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-700">
                {t('sectionB.note')}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionB.actualCompletionDate')}
                </Label>
                <Input className="h-10" placeholder="" />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionB.completionTime')}
                </Label>
                <Input className="h-10" placeholder="" />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionB.damageCause')}
                </Label>
                <Input className="h-10" placeholder="" />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionB.repairAction')}
                </Label>
                <Input className="h-10" placeholder="" />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionB.proofOfRepair')}
                </Label>
                <div className="grid grid-cols-2 gap-4">
                  <div className="border-2 border-dashed border-blue-300 rounded-lg p-6 text-center bg-blue-50">
                    <input
                      type="file"
                      id="before-upload"
                      multiple
                      accept="image/*"
                      onChange={handleBeforePhotoUpload}
                      className="hidden"
                    />
                    <div className="text-blue-600 mb-2">📷</div>
                    <div className="text-sm text-blue-600 font-medium">
                      {t('sectionB.beforePhoto')}
                    </div>
                    <label htmlFor="before-upload">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="mt-2 bg-blue-600 text-white border-blue-600 hover:bg-blue-700"
                        asChild
                      >
                        <span className="cursor-pointer">
                          {t('actions.upload')}
                        </span>
                      </Button>
                    </label>
                    {beforePhotos.length > 0 && (
                      <div className="mt-2 text-xs text-green-600">
                        {beforePhotos.length} file(s) uploaded
                      </div>
                    )}
                  </div>
                  <div className="border-2 border-dashed border-green-300 rounded-lg p-6 text-center bg-green-50">
                    <input
                      type="file"
                      id="after-upload"
                      multiple
                      accept="image/*"
                      onChange={handleAfterPhotoUpload}
                      className="hidden"
                    />
                    <div className="text-green-600 mb-2">📷</div>
                    <div className="text-sm text-green-600 font-medium">
                      {t('sectionB.afterPhoto')}
                    </div>
                    <label htmlFor="after-upload">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="mt-2 bg-green-600 text-white border-green-600 hover:bg-green-700"
                        asChild
                      >
                        <span className="cursor-pointer">
                          {t('actions.upload')}
                        </span>
                      </Button>
                    </label>
                    {afterPhotos.length > 0 && (
                      <div className="mt-2 text-xs text-green-600">
                        {afterPhotos.length} file(s) uploaded
                      </div>
                    )}
                  </div>
                </div>
                <p className="text-xs text-gray-500 text-center mt-2">
                  {t('fileUpload.acceptedFormats')}
                </p>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="repairCostRm"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionB.repairCost')}
                </Label>
                <Input
                  id="repairCostRm"
                  {...form.register('repairCostRm')}
                  className="h-10"
                  placeholder={t('placeholders.repairCost')}
                  type="number"
                  step="0.01"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Important Note */}
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="text-orange-600 mt-1">⚠️</div>
              <div className="text-sm">
                <div className="font-medium text-orange-800 mb-1">
                  {t('notes.title')}
                </div>
                <ul className="space-y-1 text-orange-700">
                  <li>• {t('notes.required')}</li>
                  <li>• {t('notes.proofRequired')}</li>
                  <li>• {t('notes.autoSubmit')}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-between items-center pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="px-6"
          >
            {t('buttons.back')}
          </Button>
          <Button
            type="submit"
            disabled={
              editMode
                ? updateComplaintMutation.isPending
                : createComplaintMutation.isPending
            }
            className="px-8 bg-blue-600 hover:bg-blue-700"
          >
            {(
              editMode
                ? updateComplaintMutation.isPending
                : createComplaintMutation.isPending
            ) ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {editMode ? t('buttons.updating') : t('buttons.submitting')}
              </>
            ) : (
              <>{editMode ? t('buttons.update') : t('buttons.submit')}</>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
