'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useUpdateComplaint } from '../hooks/use-complaints-simple';
import { CreateComplaintInput, createComplaintInputSchema } from '../schemas';

interface RepairInformationFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  complaintId: string;
  initialData?: Partial<CreateComplaintInput & { repairCostRm?: string }>;
}

export function RepairInformationForm({
  onSuccess,
  onCancel,
  complaintId,
  initialData,
}: RepairInformationFormProps) {
  const [afterP<PERSON><PERSON>, setAfterPhotos] = useState<File[]>([]);
  const updateComplaintMutation = useUpdateComplaint();
  const t = useTranslations('complaints.form');

  const form = useForm<CreateComplaintInput>({
    resolver: zodResolver(createComplaintInputSchema),
    defaultValues: {
      email: initialData?.email || '',
      damageComplaintDate: initialData?.damageComplaintDate || new Date(),
      expectedCompletionDate: initialData?.expectedCompletionDate || new Date(),
      agency: initialData?.agency || '',
      contractorCompanyName: initialData?.contractorCompanyName || '',
      location: initialData?.location || '',
      description: initialData?.description || '',
      mantrapLocation: initialData?.mantrapLocation || '',
      contactNumber: initialData?.contactNumber || '',
      contractorEmail: initialData?.contractorEmail || '',
      contractorContactNumber: initialData?.contractorContactNumber || '',
      noPmaLif: initialData?.noPmaLif || '',
      actualCompletionDate: initialData?.actualCompletionDate || undefined,
      completionNotes: initialData?.completionNotes || '',      status: (initialData?.status || 'open') as
        | 'open'
        | 'closed',
      statusNotes: initialData?.statusNotes || '',
      contractorNotes: initialData?.contractorNotes || '',
      repairCostRm: initialData?.repairCostRm || '',
    },
  });

  const handleAfterPhotoUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter((file) => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      const isValidType = ['image/jpeg', 'image/png', 'image/jpg'].includes(
        file.type,
      );
      return isValidSize && isValidType;
    });
    setAfterPhotos((prev) => [...prev, ...validFiles]);
  };

  const removeAfterPhoto = (index: number) => {
    setAfterPhotos((prev) => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: CreateComplaintInput) => {
    try {
      await updateComplaintMutation.mutateAsync({
        id: complaintId,
        data: {
          ...data,
          proofOfRepairFiles: afterPhotos,
          repairCostRm: data.repairCostRm,
        },
      });
      onSuccess();
    } catch (error) {
      console.error('Failed to update complaint:', error);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-6 max-w-5xl">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {t('title')} - {t('sectionB.title')}
        </h1>
        <p className="text-gray-600">{t('subtitle')}</p>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* B. Repair Information */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4 bg-green-50">
            <CardTitle className="text-lg font-semibold text-green-800 flex items-center gap-2">
              {t('sectionB.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 p-4 sm:p-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-700">
              {t('sectionB.note')}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 lg:gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {t('sectionB.actualCompletionDate')}
                  </Label>
                  <Input
                    {...form.register('actualCompletionDate')}
                    className="h-10"
                    placeholder=""
                    type="date"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {t('sectionB.completionTime')}
                  </Label>
                  <Input
                    {...form.register('completionNotes')}
                    className="h-10"
                    placeholder=""
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {t('sectionB.damageCause')}
                  </Label>
                  <Input
                    {...form.register('statusNotes')}
                    className="h-10"
                    placeholder=""
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    {t('sectionB.repairAction')}
                  </Label>
                  <Input
                    {...form.register('contractorNotes')}
                    className="h-10"
                    placeholder=""
                  />
                </div>
              </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">
                  {t('sectionB.proofOfRepair')}
                </Label>
                <div className="border-2 border-dashed border-green-300 rounded-lg p-4 sm:p-6 text-center bg-green-50">
                  <input
                    type="file"
                    id="after-upload"
                    multiple
                    accept="image/*"
                    onChange={handleAfterPhotoUpload}
                    className="hidden"
                  />
                  <div className="text-green-600 mb-2">📷</div>
                  <div className="text-sm text-green-600 font-medium mb-2">
                    {t('sectionB.afterPhoto')}
                  </div>
                  <label htmlFor="after-upload">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="bg-green-600 text-white border-green-600 hover:bg-green-700"
                      asChild
                    >
                      <span className="cursor-pointer">
                        {t('actions.upload')}
                      </span>
                    </Button>
                  </label>
                  {afterPhotos.length > 0 && (
                    <div className="mt-2">
                      <div className="text-xs text-green-600 mb-2">
                        {afterPhotos.length} file(s) uploaded
                      </div>
                      <div className="flex flex-wrap gap-2 justify-center">
                        {afterPhotos.map((file, index) => (
                          <div
                            key={index}
                            className="bg-white p-2 rounded border text-xs flex items-center gap-2"
                          >
                            <span className="truncate max-w-20">
                              {file.name}
                            </span>
                            <button
                              type="button"
                              onClick={() => removeAfterPhoto(index)}
                              className="text-red-500 hover:text-red-700"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                <p className="text-xs text-gray-500 text-center">
                  {t('fileUpload.acceptedFormats')}
                </p>
              </div>

              <div className="space-y-2">
                <Label
                  htmlFor="repairCostRm"
                  className="text-sm font-medium text-gray-700"
                >
                  {t('sectionB.repairCost')}
                </Label>
                <Input
                  id="repairCostRm"
                  {...form.register('repairCostRm')}
                  className="h-10"
                  placeholder={t('placeholders.repairCost')}
                  type="number"
                  step="0.01"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Important Note */}
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className="text-orange-600 mt-1">⚠️</div>
              <div className="text-sm">
                <div className="font-medium text-orange-800 mb-1">
                  {t('notes.title')}
                </div>
                <ul className="space-y-1 text-orange-700">
                  <li>• {t('notes.required')}</li>
                  <li>• {t('notes.proofRequired')}</li>
                  <li>• {t('notes.autoSubmit')}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="w-full sm:w-auto px-6"
          >
            {t('buttons.back')}
          </Button>
          <Button
            type="submit"
            disabled={updateComplaintMutation.isPending}
            className="w-full sm:w-auto px-8 bg-green-600 hover:bg-green-700"
          >
            {updateComplaintMutation.isPending ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {t('buttons.updating')}
              </>
            ) : (
              <>{t('buttons.update')}</>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
