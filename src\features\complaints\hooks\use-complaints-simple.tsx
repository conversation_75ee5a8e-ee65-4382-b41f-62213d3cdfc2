import { useUserWithProfile } from '@/hooks/use-auth';
import { supabase } from '@/lib/supabase';
import type { Database } from '@/types/database';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { CreateComplaintInput } from '../schemas';

// Define the complaint data type with related tables for the query return
export type ComplaintWithRelations = {
  id: string;
  email: string;
  number: string;
  date: string;
  expected_completion_date: string | null;
  contractor_name: string | null;
  location: string | null;
  no_pma_lif: string | null;
  description: string | null;
  involves_mantrap: boolean | null;
  actual_completion_date: string | null;
  repair_completion_time: string | null;
  cause_of_damage: string | null;
  correction_action: string | null;
  proof_of_repair_urls: string[] | null;
  repair_cost: number | null;
  status: Database['public']['Enums']['complaint_status'];
  created_at: string | null;
  updated_at: string | null;
  deleted_at: string | null;
  created_by: string | null;
  updated_by: string | null;
  deleted_by: string | null;
  contractor_id: string | null;
  pma_id: string | null;
  project_id: string | null;
  contractors?: {
    id: string;
    name: string;
    contractor_type: string;
    hotline: string | null;
    code: string | null;
  } | null;
  users?: {
    id: string;
    name: string;
    email: string;
  } | null;
};

// Hook to fetch all complaints with related data
export function useComplaints() {
  return useQuery({
    queryKey: ['complaints'],
    queryFn: async (): Promise<ComplaintWithRelations[]> => {
      const { data, error } = await supabase
        .from('complaints')
        .select(
          `
          *,
          contractors (
            id,
            name,
            contractor_type,
            hotline,
            code
          ),
          users!fk_complaints_created_by (
            id,
            name,
            email
          )
        `,
        )
        .is('deleted_at', null)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to create a new complaint
export function useCreateComplaint() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async (
      data: CreateComplaintInput & { proofOfRepairFiles?: File[] },
    ) => {
      if (!user?.id) {
        throw new Error('User must be authenticated to create complaints');
      }

      // TODO: File upload handling will be implemented later
      // For now, we'll store file names as placeholder
      const proofFileUrls =
        data.proofOfRepairFiles?.map((file) => file.name) || [];      // Generate complaint number
      const year = new Date().getFullYear();
      const complaintNumber = `DCL-${year}-${String(Math.floor(Math.random() * 9999) + 1).padStart(4, '0')}`;
      
      // Map UI status to database status
      const mapStatusToDb = (
        uiStatus?: string,
      ): Database['public']['Enums']['complaint_status'] => {
        switch (uiStatus) {
          case 'closed':
            return 'closed';
          case 'open':
          default:
            return 'open';
        }
      };
      const complaintData: Database['public']['Tables']['complaints']['Insert'] =
        {
          email: data.email,
          date: data.damageComplaintDate.toISOString().split('T')[0], // Convert to date string
          expected_completion_date: data.expectedCompletionDate
            .toISOString()
            .split('T')[0],
          contractor_name: data.contractorCompanyName,
          location: data.location,
          no_pma_lif: data.noPmaLif || '',
          description: data.description || 'No description provided',
          involves_mantrap: !!data.mantrapLocation,
          actual_completion_date: data.actualCompletionDate
            ? data.actualCompletionDate.toISOString().split('T')[0]
            : null,
          proof_of_repair_urls: proofFileUrls,
          status: mapStatusToDb(data.status),
          created_by: user.id,
          number: complaintNumber,
          // TODO: Map agency name to agency_id when needed
          // agency_id: will need to lookup agency by name
        };

      const { data: result, error } = await supabase
        .from('complaints')
        .insert(complaintData)
        .select()
        .single();

      if (error) throw error;

      return {
        ...result,
        complaintNumber,
        proofFileUrls,
      };
    },
    onSuccess: (data) => {
      // Invalidate and refetch complaints
      queryClient.invalidateQueries({ queryKey: ['complaints'] });
      toast.success('Complaint created successfully!');
      console.log('Complaint created successfully:', data);
    },
    onError: (error: Error) => {
      console.error('Failed to create complaint:', error);
      toast.error(`Failed to create complaint: ${error.message}`);
    },
  });
}

// Hook to update an existing complaint
export function useUpdateComplaint() {
  const queryClient = useQueryClient();
  const { data: user } = useUserWithProfile();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: CreateComplaintInput & { proofOfRepairFiles?: File[] };
    }) => {
      if (!user?.id) {
        throw new Error('User must be authenticated to update complaints');
      }

      // TODO: File upload handling will be implemented later
      // For now, we'll store file names as placeholder
      const proofFileUrls =
        data.proofOfRepairFiles?.map((file) => file.name) || [];      // Map UI status to database status
      const mapStatusToDb = (
        uiStatus?: string,
      ): Database['public']['Enums']['complaint_status'] => {
        switch (uiStatus) {
          case 'closed':
            return 'closed';
          case 'open':
          default:
            return 'open';
        }
      };      const complaintData = {
        email: data.email,
        date: data.damageComplaintDate?.toISOString().split('T')[0],
        expected_completion_date: data.expectedCompletionDate
          ?.toISOString()
          .split('T')[0],
        contractor_name: data.contractorCompanyName,
        location: data.location,
        no_pma_lif: data.noPmaLif || '',
        description: data.description || 'No description provided',
        involves_mantrap: !!data.mantrapLocation,
        actual_completion_date: data.actualCompletionDate
          ? data.actualCompletionDate.toISOString().split('T')[0]
          : null,
        proof_of_repair_urls: proofFileUrls,
        status: mapStatusToDb(data.status),
        // Section B fields - populate when filling repair information
        repair_completion_time: data.completionNotes || null,
        cause_of_damage: data.statusNotes || null,
        correction_action: data.contractorNotes || null,
        // Set repair cost - only if Section B data is provided
        repair_cost: data.repairCostRm ? parseFloat(data.repairCostRm) : null,
        updated_at: new Date().toISOString(),
        updated_by: user.id,
      };

      const { data: result, error } = await supabase
        .from('complaints')
        .update(complaintData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return {
        ...result,
        proofFileUrls,
      };
    },
    onSuccess: (data) => {
      // Invalidate and refetch complaints
      queryClient.invalidateQueries({ queryKey: ['complaints'] });
      toast.success('Complaint updated successfully!');
      console.log('Complaint updated successfully:', data);
    },
    onError: (error: Error) => {
      console.error('Failed to update complaint:', error);
      toast.error(`Failed to update complaint: ${error.message}`);
    },
  });
}
