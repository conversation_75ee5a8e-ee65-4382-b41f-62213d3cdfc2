// Simple UI types for complaints feature
// These are temporary UI-focused types until we connect to the real database

export interface ComplaintUI {
  id: string;
  complaintNumber: string;
  email: string;
  contactNumber?: string;
  damageComplaintDate: string;
  expectedCompletionDate: string;
  actualCompletionDate?: string;
  agency: string;
  contractorCompanyName: string;
  contractorEmail?: string;
  contractorContactNumber?: string;
  location: string;
  mantrapLocation?: string;
  noPmaLif?: string;
  description?: string;
  completionNotes?: string;
  status: 'open' | 'closed';
  createdAt: string;
  proofOfRepairFiles?: Array<{
    name: string;
    url: string;
    size: number;
  }>;
  // Section completion tracking
  sectionACompleted: boolean;
  sectionBCompleted: boolean;
  repairCost: number;
  // Additional fields for Section B
  causeOfDamage?: string;
  correctionAction?: string;
  repairCompletionTime?: string;
}

export interface ComplaintTableProps {
  onViewComplaint: (complaint: ComplaintUI) => void;
  onEditComplaint?: (complaint: ComplaintUI) => void;
}