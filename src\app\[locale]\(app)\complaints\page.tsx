'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ComplaintWithRelations, useComplaints } from '@/features/complaints/hooks/use-complaints-simple';
import { ComplaintUI } from '@/features/complaints/types/ui-types';
import {
  AlertCircle,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Edit,
  FileText,
  Filter,
  Plus
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';

export default function ComplaintsPage() {
  const router = useRouter();

  // Translations
  const t = useTranslations('complaints');
  
  // Fetch real complaints data
  const { data: complaintsData = [], isLoading, error } = useComplaints();
  // Helper function to determine if Section A is completed
  const isSectionACompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.email &&
      complaint.date &&
      complaint.expected_completion_date &&
      complaint.contractor_name &&
      complaint.location &&
      complaint.no_pma_lif &&
      complaint.description
    );
  };

  // Helper function to determine if Section B is completed
  const isSectionBCompleted = (complaint: ComplaintWithRelations): boolean => {
    return !!(
      complaint.actual_completion_date &&
      complaint.repair_completion_time &&
      complaint.cause_of_damage &&
      complaint.correction_action &&
      complaint.proof_of_repair_urls &&
      complaint.proof_of_repair_urls.length > 0
    );
  };

  // Convert database complaints to UI format
  const complaints: ComplaintUI[] = complaintsData.map((complaint) => {
    const sectionAComplete = isSectionACompleted(complaint);
    const sectionBComplete = isSectionBCompleted(complaint);
    
    return {
      id: complaint.id,
      complaintNumber:
        complaint.number ||
        `DCL-${new Date(complaint.created_at || '').getFullYear()}-${complaint.id.slice(-4)}`,
      email: complaint.email,
      contactNumber: undefined,
      damageComplaintDate:
        complaint.date || complaint.created_at || new Date().toISOString(),
      expectedCompletionDate:
        complaint.expected_completion_date ||
        complaint.created_at ||
        new Date().toISOString(),
      actualCompletionDate: complaint.actual_completion_date || undefined,
      agency: 'N/A', // Agency info not included in current schema
      contractorCompanyName: complaint.contractor_name || 'N/A',
      contractorEmail: undefined,
      contractorContactNumber: undefined,
      location: complaint.location || 'N/A',
      mantrapLocation: complaint.involves_mantrap ? 'Yes' : undefined,
      noPmaLif: complaint.no_pma_lif || undefined,
      description: complaint.description || undefined,
      completionNotes: undefined,
      status: complaint.status as 'open' | 'closed',
      createdAt: complaint.created_at || new Date().toISOString(),
      sectionACompleted: sectionAComplete,
      sectionBCompleted: sectionBComplete,
      repairCost: sectionBComplete ? (complaint.repair_cost || 0) : 0, // Show 0 until Section B completed
      causeOfDamage: complaint.cause_of_damage || undefined,
      correctionAction: complaint.correction_action || undefined,
      repairCompletionTime: complaint.repair_completion_time || undefined,
      proofOfRepairFiles:
        complaint.proof_of_repair_urls?.map((url: string, index: number) => ({
          name: `Repair Evidence ${index + 1}`,
          url,
          size: 0, // Size not stored in current schema
        })) || [],
    };  });// Calculate stats
  const totalReports = complaints.length;
  const openReports = complaints.filter(
    (c: ComplaintUI) => c.status === 'open',
  ).length;
  const closedReports = complaints.filter(
    (c: ComplaintUI) => c.status === 'closed',
  ).length;  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            {t('status.open')}
          </Badge>
        );
      case 'closed':
        return (
          <Badge variant="secondary" className="bg-red-100 text-red-800">
            {t('status.closed')}
          </Badge>
        );
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };
  // Render follow-up status
  const renderFollowUpStatus = (complaint: ComplaintUI) => {
    if (complaint.sectionACompleted && complaint.sectionBCompleted) {
      return (
        <div className="flex items-center gap-1 text-green-600">
          <CheckCircle className="h-4 w-4" />
          <span className="text-sm font-medium">Complete</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-1 text-blue-600">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm font-medium">Upload</span>
        </div>
      );
    }
  };  const handleViewComplaint = (complaint: ComplaintUI) => {
    // Navigate to complaint details page or open modal
    router.push(`/complaints/${complaint.id}`);
  };

  const handleEditComplaintFromTable = (complaint: ComplaintUI) => {
    router.push(`/complaints/${complaint.id}/edit`);
  };
  const handleCreateNewComplaint = () => {
    router.push('/complaints/new');
  };

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
            Loading complaints...
          </div>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex flex-col items-center gap-2 py-8">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <p className="text-red-600">Failed to load complaints</p>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : 'Unknown error occurred'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
            <p className="text-gray-600 text-sm">{t('subtitle')}</p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-blue-700">
                  {totalReports}
                </p>
                <p className="text-sm text-blue-600">
                  {t('stats.totalReports')}
                </p>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>        <Card className="bg-green-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-green-700">
                  {openReports}
                </p>
                <p className="text-sm text-green-600">
                  {t('status.open')}
                </p>
              </div>
              <Clock className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-red-700">{closedReports}</p>
                <p className="text-sm text-red-600">{t('status.closed')}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Complaint Reports Table */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="pb-4">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg font-semibold">
                  {t('table.title')}
                </CardTitle>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    {t('table.advancedFilter')}
                  </Button>{' '}
                  <Button
                    size="sm"
                    className="bg-blue-600 hover:bg-blue-700"
                    onClick={handleCreateNewComplaint}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t('table.newReport')}
                  </Button>
                </div>
              </div>
              <p className="text-sm text-gray-600">{t('table.subtitle')}</p>
            </CardHeader>

            <CardContent className="p-0">
              <Table>                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-medium">
                      {t('table.reportId')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.dateSubmitted')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.pmaNumber')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.location')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.status')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.followUp')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.cost')}
                    </TableHead>
                    <TableHead className="font-medium">
                      {t('table.actions')}
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {complaints.map((complaint: ComplaintUI) => (
                    <TableRow key={complaint.id} className="hover:bg-gray-50">
                      <TableCell>
                        <Button
                          variant="link"
                          className="p-0 h-auto text-blue-600 hover:text-blue-800"
                          onClick={() => handleViewComplaint(complaint)}
                        >
                          {complaint.complaintNumber}
                        </Button>
                      </TableCell>
                      <TableCell className="text-sm">
                        {complaint.damageComplaintDate}
                      </TableCell>
                      <TableCell className="text-sm">
                        {complaint.noPmaLif}
                      </TableCell>
                      <TableCell className="text-sm">
                        {complaint.location}
                      </TableCell>                      <TableCell>{getStatusBadge(complaint.status)}</TableCell>                      <TableCell>
                        {renderFollowUpStatus(complaint)}
                      </TableCell>
                      <TableCell className="text-sm">
                        RM {complaint.repairCost.toFixed(2)}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() =>
                              handleEditComplaintFromTable(complaint)
                            }
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              {/* Pagination */}
              <div className="flex justify-between items-center p-4 border-t">
                <span className="text-sm text-gray-600">
                  {t('pagination.showing', {
                    start: '1',
                    end: '5',
                    total: '12',
                  })}
                </span>
                <div className="flex gap-1">
                  <Button variant="outline" size="sm">
                    <ChevronLeft className="h-4 w-4" />
                    {t('pagination.previous')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-blue-600 text-white"
                  >
                    1
                  </Button>
                  <Button variant="outline" size="sm">
                    2
                  </Button>
                  <Button variant="outline" size="sm">
                    3
                  </Button>
                  <Button variant="outline" size="sm">
                    {t('pagination.next')}
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

/**
 * Complaint Status Logic:
 * - "open" (green): Appears when contractor fills in Section A (complaint information). 
 *   Indicates that the complaint report has been submitted but Section B (repair information) 
 *   is not yet completed.
 * - "closed" (red): Appears when contractor completes Section B (repair information) by 
 *   editing the complaint and providing repair details. This means the case is fully resolved.
 * 
 * Status flow: Section A completed -> "open" -> Section B completed -> "closed"
 */
